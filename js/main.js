// 王国争霸H5 - 主游戏文件

class Game {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        this.currentScreen = 'loading';
        
        // 游戏系统
        this.engine = null;
        this.resources = null;
        this.buildings = null;
        this.army = null;
        this.tech = null;
        this.quests = null;
        this.ui = null;
        
        // 玩家数据
        this.player = {
            level: 1,
            exp: 0,
            expToNext: 100
        };
        
        this.playtime = 0;
        this.startTime = Date.now();
        
        this.init();
    }

    // 初始化游戏
    async init() {
        try {
            console.log('Initializing game...');
            
            // 显示加载进度
            this.updateLoadingProgress(10, '初始化游戏引擎...');
            await Utils.delay(100);
            
            // 初始化游戏引擎
            this.engine = new GameEngine();
            this.updateLoadingProgress(20, '加载游戏数据...');
            await Utils.delay(100);
            
            // 初始化游戏系统
            this.resources = new ResourceManager();
            this.updateLoadingProgress(40, '初始化建筑系统...');
            await Utils.delay(100);
            
            this.buildings = new BuildingSystem();
            this.updateLoadingProgress(50, '初始化军队系统...');
            await Utils.delay(100);

            this.army = new ArmySystem();
            this.updateLoadingProgress(60, '初始化科技系统...');
            await Utils.delay(100);

            this.tech = new TechSystem();
            this.updateLoadingProgress(70, '初始化任务系统...');
            await Utils.delay(100);

            this.quests = new QuestSystem();
            this.updateLoadingProgress(75, '初始化战斗系统...');
            await Utils.delay(100);

            this.battle = new BattleSystem();
            this.updateLoadingProgress(80, '初始化UI系统...');
            await Utils.delay(100);

            this.ui = new UIManager();
            this.updateLoadingProgress(90, '加载存档系统...');
            await Utils.delay(100);
            
            // 初始化存档系统
            SaveManager.init();
            this.updateLoadingProgress(90, '完成初始化...');
            await Utils.delay(100);
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.updateLoadingProgress(100, '游戏加载完成！');
            await Utils.delay(500);
            
            this.isInitialized = true;
            this.showMainMenu();
            
            console.log('Game initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('游戏初始化失败，请刷新页面重试。');
        }
    }

    // 更新加载进度
    updateLoadingProgress(percentage, text) {
        const progressBar = Utils.$('#loadingProgress');
        const loadingText = Utils.$('#loadingText');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (loadingText) {
            Utils.setContent(loadingText, text);
        }
    }

    // 显示主菜单
    showMainMenu() {
        this.currentScreen = 'menu';
        Utils.hide('#loadingScreen');
        Utils.show('#menuScreen');
        
        // 检查是否有存档
        const continueBtn = Utils.$('#continueBtn');
        if (continueBtn) {
            if (SaveManager.hasSaveData()) {
                continueBtn.style.display = 'block';
            } else {
                continueBtn.style.display = 'none';
            }
        }
    }

    // 开始新游戏
    startNewGame() {
        console.log('Starting new game...');
        
        // 重置游戏数据
        this.resetGameData();
        
        // 创建新游戏存档
        SaveManager.createNewGame();
        
        // 开始游戏
        this.startGame();
    }

    // 继续游戏
    continueGame() {
        console.log('Loading saved game...');
        
        const saveData = SaveManager.loadGame();
        if (saveData) {
            this.loadGameData(saveData);
            this.startGame();
        } else {
            this.showError('无法加载存档，请开始新游戏。');
        }
    }

    // 开始游戏
    startGame() {
        this.currentScreen = 'playing';
        this.isRunning = true;
        
        Utils.hide('#menuScreen');
        Utils.show('#gameScreen');
        
        // 启动游戏引擎
        this.engine.start();
        
        // 更新UI
        this.updateUI();
        
        // 发送游戏开始事件
        GameEvents.emit(GAME_EVENTS.GAME_START);
        
        console.log('Game started');
    }

    // 暂停游戏
    pauseGame() {
        if (!this.isRunning) return;
        
        this.engine.pause();
        GameEvents.emit(GAME_EVENTS.GAME_PAUSE);
        
        console.log('Game paused');
    }

    // 恢复游戏
    resumeGame() {
        if (!this.isRunning) return;
        
        this.engine.resume();
        GameEvents.emit(GAME_EVENTS.GAME_RESUME);
        
        console.log('Game resumed');
    }

    // 保存游戏
    saveGame() {
        if (!this.isRunning) return;
        
        const success = SaveManager.saveGame();
        if (success) {
            this.ui.showMessage('游戏已保存', MESSAGE_TYPES.SUCCESS);
        } else {
            this.ui.showMessage('保存失败', MESSAGE_TYPES.ERROR);
        }
    }

    // 返回主菜单
    backToMenu() {
        this.isRunning = false;
        this.engine.stop();
        
        Utils.hide('#gameScreen');
        this.showMainMenu();
        
        console.log('Returned to main menu');
    }

    // 重置游戏数据
    resetGameData() {
        this.player = {
            level: 1,
            exp: 0,
            expToNext: 100
        };
        
        this.playtime = 0;
        this.startTime = Date.now();
        
        // 重置各个系统
        if (this.resources) this.resources.reset();
        if (this.buildings) this.buildings.reset();
        if (this.army) this.army.reset();
        if (this.tech) this.tech.reset();
        if (this.quests) this.quests.reset();
    }

    // 加载游戏数据
    loadGameData(data) {
        if (data.player) {
            this.player = { ...data.player };
        }
        
        if (data.playtime) {
            this.playtime = data.playtime;
        }
        
        // 加载各系统数据
        if (this.resources && data.resources) {
            this.resources.loadData(data.resources);
        }
        
        if (this.buildings && data.buildings) {
            this.buildings.loadData(data.buildings);
        }
        
        // TODO: 加载其他系统数据
    }

    // 更新UI
    updateUI() {
        // 更新玩家信息
        Utils.setContent('#playerLevel', this.player.level);
        
        // 更新经验条
        const expProgress = Utils.$('#expProgress');
        if (expProgress) {
            const percentage = (this.player.exp / this.player.expToNext) * 100;
            expProgress.style.width = percentage + '%';
        }
        
        // 更新人口信息
        const currentPop = this.getCurrentPopulation();
        const maxPop = this.getMaxPopulation();
        Utils.setContent('#populationCurrent', currentPop);
        Utils.setContent('#populationMax', maxPop);
    }

    // 获取当前人口
    getCurrentPopulation() {
        // TODO: 计算当前军队人口
        return 10;
    }

    // 获取最大人口
    getMaxPopulation() {
        let maxPop = 50; // 基础人口
        
        // 根据建筑计算人口上限
        if (this.buildings) {
            const buildings = this.buildings.getAllBuildings();
            for (const building of buildings) {
                if (building.type === 'castle') {
                    maxPop += building.level * 20;
                }
            }
        }
        
        return maxPop;
    }

    // 获得经验
    gainExp(amount) {
        this.player.exp += amount;
        
        // 检查升级
        while (this.player.exp >= this.player.expToNext) {
            this.levelUp();
        }
        
        this.updateUI();
        GameEvents.emit(GAME_EVENTS.EXP_GAIN, amount);
    }

    // 升级
    levelUp() {
        this.player.exp -= this.player.expToNext;
        this.player.level++;
        
        // 计算下一级所需经验
        this.player.expToNext = GameData.levelExp[this.player.level] || 
                                (this.player.expToNext * 1.5);
        
        this.ui.showMessage(`恭喜升级到${this.player.level}级！`, MESSAGE_TYPES.SUCCESS);
        GameEvents.emit(GAME_EVENTS.LEVEL_UP, this.player.level);
    }

    // 设置事件监听
    setupEventListeners() {
        // 主菜单按钮
        Utils.on('#newGameBtn', 'click', () => this.startNewGame());
        Utils.on('#continueBtn', 'click', () => this.continueGame());
        Utils.on('#helpBtn', 'click', () => this.showHelp());
        
        // 游戏菜单按钮
        Utils.on('#menuBtn', 'click', () => this.ui.showGameMenu());
        Utils.on('#saveBtn', 'click', () => this.saveGame());
        Utils.on('#loadBtn', 'click', () => this.continueGame());
        Utils.on('#backToMenuBtn', 'click', () => this.backToMenu());
        Utils.on('#resumeBtn', 'click', () => this.ui.hideGameMenu());
        
        // 操作栏按钮
        Utils.on('#buildBtn', 'click', () => this.ui.togglePanel('building'));
        Utils.on('#armyBtn', 'click', () => this.ui.togglePanel('army'));
        Utils.on('#techBtn', 'click', () => this.ui.togglePanel('tech'));
        Utils.on('#battleBtn', 'click', () => this.ui.togglePanel('battle'));
        Utils.on('#questBtn', 'click', () => this.ui.togglePanel('quest'));
        
        // 关闭面板按钮
        Utils.$$('.close-btn').forEach(btn => {
            Utils.on(btn, 'click', (e) => {
                const panel = e.target.closest('.side-panel, .modal');
                if (panel) {
                    Utils.hide(panel);
                }
            });
        });

        // 建筑分类按钮
        Utils.$$('.category-btn').forEach(btn => {
            Utils.on(btn, 'click', (e) => {
                // 移除其他按钮的active类
                Utils.$$('.category-btn').forEach(b => Utils.removeClass(b, 'active'));
                // 添加当前按钮的active类
                Utils.addClass(e.target, 'active');
                // 重新加载建筑面板
                if (this.ui) {
                    this.ui.loadBuildingPanel();
                }
            });
        });

        // 标签页按钮
        Utils.$$('.tab-btn').forEach(btn => {
            Utils.on(btn, 'click', (e) => {
                const tabContainer = e.target.closest('.army-tabs, .quest-tabs');
                if (tabContainer) {
                    // 移除同组其他按钮的active类
                    tabContainer.querySelectorAll('.tab-btn').forEach(b => Utils.removeClass(b, 'active'));
                    // 添加当前按钮的active类
                    Utils.addClass(e.target, 'active');

                    // 重新加载对应面板
                    if (this.ui) {
                        if (tabContainer.classList.contains('army-tabs')) {
                            this.ui.loadArmyPanel();
                        } else if (tabContainer.classList.contains('quest-tabs')) {
                            this.ui.loadQuestPanel();
                        }
                    }
                }
            });
        });

        // 战斗模式按钮
        Utils.$$('.mode-btn').forEach(btn => {
            Utils.on(btn, 'click', (e) => {
                const modeContainer = e.target.closest('.battle-modes');
                if (modeContainer) {
                    // 移除其他按钮的active类
                    modeContainer.querySelectorAll('.mode-btn').forEach(b => Utils.removeClass(b, 'active'));
                    // 添加当前按钮的active类
                    Utils.addClass(e.target, 'active');

                    // 重新加载战斗面板
                    if (this.ui) {
                        this.ui.loadBattlePanel();
                    }
                }
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (this.currentScreen === 'playing') {
                switch (e.code) {
                    case 'Escape':
                        this.ui.toggleGameMenu();
                        break;
                    case 'KeyB':
                        this.ui.togglePanel('building');
                        break;
                    case 'KeyA':
                        this.ui.togglePanel('army');
                        break;
                    case 'KeyT':
                        this.ui.togglePanel('tech');
                        break;
                    case 'KeyF':
                        this.ui.togglePanel('battle');
                        break;
                    case 'KeyQ':
                        this.ui.togglePanel('quest');
                        break;
                }
            }
        });
    }

    // 显示帮助
    showHelp() {
        this.ui.showMessage('游戏帮助：使用鼠标点击建造建筑，管理资源，训练军队，征战四方！', MESSAGE_TYPES.INFO);
    }

    // 显示错误
    showError(message) {
        console.error(message);
        if (this.ui) {
            this.ui.showMessage(message, MESSAGE_TYPES.ERROR);
        } else {
            alert(message);
        }
    }

    // 获取游戏状态
    getGameState() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            currentScreen: this.currentScreen,
            player: this.player,
            playtime: this.playtime
        };
    }
}

// UI管理器（简化版）
class UIManager {
    constructor() {
        this.currentPanel = null;
        this.init();
    }

    init() {
        console.log('UI Manager initialized');
    }

    // 切换面板
    togglePanel(panelName) {
        const panel = Utils.$(`#${panelName}Panel`);
        if (!panel) return;

        if (this.currentPanel === panelName) {
            this.hidePanel(panelName);
        } else {
            this.showPanel(panelName);
        }
    }

    // 显示面板
    showPanel(panelName) {
        // 隐藏当前面板
        if (this.currentPanel) {
            this.hidePanel(this.currentPanel);
        }

        const panel = Utils.$(`#${panelName}Panel`);
        if (panel) {
            Utils.addClass(panel, 'show');
            this.currentPanel = panelName;
            
            // 加载面板内容
            this.loadPanelContent(panelName);
        }
    }

    // 隐藏面板
    hidePanel(panelName) {
        const panel = Utils.$(`#${panelName}Panel`);
        if (panel) {
            Utils.removeClass(panel, 'show');
            if (this.currentPanel === panelName) {
                this.currentPanel = null;
            }
        }
    }

    // 加载面板内容
    loadPanelContent(panelName) {
        switch (panelName) {
            case 'building':
                this.loadBuildingPanel();
                break;
            case 'army':
                this.loadArmyPanel();
                break;
            case 'tech':
                this.loadTechPanel();
                break;
            case 'quest':
                this.loadQuestPanel();
                break;
            case 'battle':
                this.loadBattlePanel();
                break;
        }
    }

    // 加载建筑面板
    loadBuildingPanel() {
        const buildingList = Utils.$('#buildingList');
        if (!buildingList) return;

        buildingList.innerHTML = '';

        // 获取当前分类
        const activeCategory = Utils.$('.category-btn.active');
        const category = activeCategory ? activeCategory.dataset.category : 'resource';

        // 过滤建筑
        const buildings = Object.values(GameData.buildings).filter(
            building => building.category === category
        );

        // 生成建筑项
        for (const building of buildings) {
            const buildingItem = this.createBuildingItem(building);
            buildingList.appendChild(buildingItem);
        }
    }

    // 创建建筑项
    createBuildingItem(buildingData) {
        const item = Utils.createElement('div', 'building-item');
        
        // 检查是否可建造
        const canBuild = window.Game.player.level >= buildingData.unlockLevel &&
                        window.Game.resources.hasResources(buildingData.cost);
        
        if (!canBuild) {
            Utils.addClass(item, 'disabled');
        }

        item.innerHTML = `
            <div class="building-header">
                <span class="building-name">${buildingData.icon} ${buildingData.name}</span>
                <span class="building-cost">${this.formatCost(buildingData.cost)}</span>
            </div>
            <div class="building-desc">${buildingData.description}</div>
            <div class="building-stats">
                ${buildingData.produces ? `产出: ${this.formatProduction(buildingData.produces)}` : ''}
                ${buildingData.buildTime ? `建造时间: ${buildingData.buildTime}秒` : ''}
            </div>
        `;

        if (canBuild) {
            item.onclick = () => {
                window.Game.buildings.enterBuildMode(buildingData.id);
                this.hidePanel('building');
            };
        }

        return item;
    }

    // 加载军队面板
    loadArmyPanel() {
        const armyContent = Utils.$('#armyContent');
        if (!armyContent) return;

        // 获取当前标签
        const activeTab = Utils.$('.army-tabs .tab-btn.active');
        const tab = activeTab ? activeTab.dataset.tab : 'recruit';

        if (tab === 'recruit') {
            this.loadArmyRecruitTab(armyContent);
        } else if (tab === 'manage') {
            this.loadArmyManageTab(armyContent);
        }
    }

    // 加载军队招募标签
    loadArmyRecruitTab(container) {
        container.innerHTML = '';

        const unitList = Utils.createElement('div', 'unit-list');

        for (const [unitId, unitData] of Object.entries(GameData.units)) {
            const canTrain = window.Game.player.level >= unitData.unlockLevel &&
                           window.Game.resources.hasResources(unitData.cost);

            const unitItem = Utils.createElement('div', 'unit-item');
            if (!canTrain) {
                Utils.addClass(unitItem, 'disabled');
            }

            unitItem.innerHTML = `
                <div class="unit-info">
                    <div class="unit-name">${unitData.icon} ${unitData.name}</div>
                    <div class="unit-stats">
                        攻击: ${unitData.attack} | 防御: ${unitData.defense} | 生命: ${unitData.health}
                    </div>
                    <div class="unit-cost">${this.formatCost(unitData.cost)}</div>
                </div>
                <div class="unit-actions">
                    <button class="btn btn-primary" ${canTrain ? '' : 'disabled'}>训练</button>
                </div>
            `;

            if (canTrain) {
                const trainBtn = unitItem.querySelector('.btn');
                trainBtn.onclick = () => {
                    window.Game.army.trainUnit(unitId);
                    this.loadArmyPanel(); // 刷新面板
                };
            }

            unitList.appendChild(unitItem);
        }

        container.appendChild(unitList);
    }

    // 加载军队管理标签
    loadArmyManageTab(container) {
        container.innerHTML = '';

        const units = window.Game.army.getAllUnits();

        if (units.length === 0) {
            container.innerHTML = '<p>暂无军队</p>';
            return;
        }

        const unitList = Utils.createElement('div', 'unit-list');

        for (const unit of units) {
            const unitItem = Utils.createElement('div', 'unit-item');

            unitItem.innerHTML = `
                <div class="unit-info">
                    <div class="unit-name">${unit.data.icon} ${unit.data.name}</div>
                    <div class="unit-stats">
                        ${unit.isTraining ? `训练中... ${Math.ceil(unit.trainTimeRemaining / 1000)}秒` :
                          `攻击: ${unit.getAttack()} | 防御: ${unit.getDefense()} | 生命: ${unit.health}/${unit.maxHealth}`}
                    </div>
                </div>
                <div class="unit-actions">
                    ${!unit.isTraining ? '<button class="btn btn-secondary">解散</button>' : ''}
                </div>
            `;

            if (!unit.isTraining) {
                const dismissBtn = unitItem.querySelector('.btn');
                if (dismissBtn) {
                    dismissBtn.onclick = () => {
                        window.Game.army.dismissUnit(unit.id);
                        this.loadArmyPanel(); // 刷新面板
                    };
                }
            }

            unitList.appendChild(unitItem);
        }

        container.appendChild(unitList);
    }

    // 加载科技面板
    loadTechPanel() {
        const techTree = Utils.$('#techTree');
        if (!techTree) return;

        techTree.innerHTML = '';

        const techs = window.Game.tech.getAllTechs();

        for (const tech of techs) {
            const canResearch = window.Game.tech.canResearch(tech.id);
            const techItem = Utils.createElement('div', 'tech-item');

            if (tech.isResearched) {
                Utils.addClass(techItem, 'researched');
            } else if (!canResearch) {
                Utils.addClass(techItem, 'disabled');
            }

            techItem.innerHTML = `
                <div class="tech-name">${tech.data.name}</div>
                <div class="tech-desc">${tech.data.description}</div>
                <div class="tech-cost">${this.formatCost(tech.data.cost)}</div>
                ${tech.isResearching ? `<div class="tech-progress">研发中... ${Math.ceil(tech.researchTimeRemaining / 1000)}秒</div>` : ''}
            `;

            if (canResearch && !tech.isResearching) {
                techItem.onclick = () => {
                    window.Game.tech.researchTech(tech.id);
                    this.loadTechPanel(); // 刷新面板
                };
            }

            techTree.appendChild(techItem);
        }
    }

    // 加载任务面板
    loadQuestPanel() {
        const questContent = Utils.$('#questContent');
        if (!questContent) return;

        // 获取当前标签
        const activeTab = Utils.$('.quest-tabs .tab-btn.active');
        const tab = activeTab ? activeTab.dataset.tab : 'main';

        let quests = [];
        if (tab === 'main') {
            quests = window.Game.quests.getMainQuests();
        } else if (tab === 'daily') {
            quests = window.Game.quests.getDailyQuests();
        } else if (tab === 'achievement') {
            quests = window.Game.quests.getCompletedQuests();
        }

        questContent.innerHTML = '';

        if (quests.length === 0) {
            questContent.innerHTML = '<p>暂无任务</p>';
            return;
        }

        const questList = Utils.createElement('div', 'quest-list');

        for (const quest of quests) {
            const questItem = Utils.createElement('div', 'quest-item');

            if (quest.isCompleted) {
                Utils.addClass(questItem, 'completed');
            }

            questItem.innerHTML = `
                <div class="quest-header">
                    <div class="quest-name">${quest.name}</div>
                    <div class="quest-reward">${this.formatRewards(quest.rewards)}</div>
                </div>
                <div class="quest-desc">${quest.description}</div>
                <div class="quest-progress">
                    进度: ${quest.progress}/${quest.count} (${Math.floor(quest.getProgressPercentage())}%)
                </div>
            `;

            questList.appendChild(questItem);
        }

        questContent.appendChild(questList);
    }

    // 加载战斗面板
    loadBattlePanel() {
        const battleContent = Utils.$('#battleContent');
        if (!battleContent) return;

        // 获取当前模式
        const activeMode = Utils.$('.battle-modes .mode-btn.active');
        const mode = activeMode ? activeMode.dataset.mode : 'campaign';

        if (mode === 'campaign') {
            this.loadCampaignBattles(battleContent);
        }
        // TODO: 添加其他战斗模式
    }

    // 加载关卡挑战
    loadCampaignBattles(container) {
        container.innerHTML = '';

        const stages = window.Game.battle.getAvailableStages();

        if (stages.length === 0) {
            container.innerHTML = '<p>暂无可挑战的关卡</p>';
            return;
        }

        const battleList = Utils.createElement('div', 'battle-list');

        for (const stage of stages) {
            const battleItem = Utils.createElement('div', 'battle-item');

            battleItem.innerHTML = `
                <div class="battle-header">
                    <div class="battle-name">${stage.name}</div>
                    <div class="battle-difficulty difficulty-${stage.difficulty}">${stage.difficulty}</div>
                </div>
                <div class="battle-desc">${stage.description}</div>
                <div class="battle-rewards">奖励: ${this.formatRewards(stage.rewards)}</div>
                <div class="battle-enemies">
                    敌军: ${stage.enemies.map(e => `${e.count}个${GameData.units[e.type]?.name || e.type}`).join(', ')}
                </div>
                <div class="battle-actions">
                    <button class="btn btn-primary">挑战</button>
                </div>
            `;

            const challengeBtn = battleItem.querySelector('.btn');
            challengeBtn.onclick = () => {
                this.startBattle(stage.id);
            };

            battleList.appendChild(battleItem);
        }

        container.appendChild(battleList);
    }

    // 开始战斗
    startBattle(stageId) {
        // 获取可用的军队
        const availableUnits = window.Game.army.getAvailableUnits();

        if (availableUnits.length === 0) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '没有可用的军队！请先训练军队。'
            });
            return;
        }

        // 简化版：使用所有可用军队
        const success = window.Game.battle.startBattle(stageId, availableUnits);

        if (success) {
            // 隐藏战斗面板
            this.hidePanel('battle');

            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.INFO,
                text: '战斗开始！'
            });
        }
    }

    // 格式化成本
    formatCost(cost) {
        const parts = [];
        for (const [resource, amount] of Object.entries(cost)) {
            const icon = this.getResourceIcon(resource);
            parts.push(`${icon}${amount}`);
        }
        return parts.join(' ');
    }

    // 格式化产出
    formatProduction(production) {
        const parts = [];
        for (const [resource, amount] of Object.entries(production)) {
            const icon = this.getResourceIcon(resource);
            parts.push(`${icon}${amount}/分钟`);
        }
        return parts.join(' ');
    }

    // 格式化奖励
    formatRewards(rewards) {
        if (!rewards) return '';

        const parts = [];
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                parts.push(`⭐${amount}经验`);
            } else {
                const icon = this.getResourceIcon(resource);
                parts.push(`${icon}${amount}`);
            }
        }
        return parts.join(' ');
    }

    // 获取资源图标
    getResourceIcon(resource) {
        const icons = {
            gold: '💰',
            wood: '🪵',
            stone: '🪨',
            food: '🌾'
        };
        return icons[resource] || '❓';
    }

    // 显示消息
    showMessage(text, type = MESSAGE_TYPES.INFO) {
        const container = Utils.$('#messageContainer');
        if (!container) return;

        const message = Utils.createElement('div', `message ${type}`);
        Utils.setContent(message, text);
        
        container.appendChild(message);

        // 自动移除消息
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // 显示游戏菜单
    showGameMenu() {
        Utils.show('#gameMenu');
    }

    // 隐藏游戏菜单
    hideGameMenu() {
        Utils.hide('#gameMenu');
    }

    // 切换游戏菜单
    toggleGameMenu() {
        const menu = Utils.$('#gameMenu');
        if (menu) {
            Utils.toggle(menu);
        }
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, starting game...');
    window.Game = new Game();
});

// 导出
window.Game = null;
window.UIManager = UIManager;
