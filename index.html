<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>王国争霸H5 - Kingdom Battle</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/ui.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏加载界面 -->
        <div id="loadingScreen" class="screen">
            <div class="loading-content">
                <h1>王国争霸</h1>
                <div class="loading-bar">
                    <div class="loading-progress" id="loadingProgress"></div>
                </div>
                <p id="loadingText">正在加载游戏资源...</p>
            </div>
        </div>

        <!-- 主游戏界面 -->
        <div id="gameScreen" class="screen hidden">
            <!-- 顶部资源栏 -->
            <div id="resourceBar" class="resource-bar">
                <div class="resource-item">
                    <img src="assets/images/icons/gold.png" alt="金币" class="resource-icon">
                    <span id="goldAmount">1000</span>
                </div>
                <div class="resource-item">
                    <img src="assets/images/icons/wood.png" alt="木材" class="resource-icon">
                    <span id="woodAmount">500</span>
                </div>
                <div class="resource-item">
                    <img src="assets/images/icons/stone.png" alt="石材" class="resource-icon">
                    <span id="stoneAmount">300</span>
                </div>
                <div class="resource-item">
                    <img src="assets/images/icons/food.png" alt="食物" class="resource-icon">
                    <span id="foodAmount">800</span>
                </div>
                <div class="resource-item">
                    <img src="assets/images/icons/population.png" alt="人口" class="resource-icon">
                    <span id="populationAmount">50/100</span>
                </div>
            </div>

            <!-- 游戏主画布 -->
            <canvas id="gameCanvas" width="1200" height="800"></canvas>

            <!-- 底部操作栏 -->
            <div id="actionBar" class="action-bar">
                <button id="buildBtn" class="action-btn">
                    <img src="assets/images/icons/build.png" alt="建造">
                    <span>建造</span>
                </button>
                <button id="armyBtn" class="action-btn">
                    <img src="assets/images/icons/army.png" alt="军队">
                    <span>军队</span>
                </button>
                <button id="techBtn" class="action-btn">
                    <img src="assets/images/icons/tech.png" alt="科技">
                    <span>科技</span>
                </button>
                <button id="battleBtn" class="action-btn">
                    <img src="assets/images/icons/battle.png" alt="战斗">
                    <span>战斗</span>
                </button>
                <button id="questBtn" class="action-btn">
                    <img src="assets/images/icons/quest.png" alt="任务">
                    <span>任务</span>
                </button>
            </div>
        </div>

        <!-- 建造界面 -->
        <div id="buildingPanel" class="panel hidden">
            <div class="panel-header">
                <h3>建造建筑</h3>
                <button class="close-btn" onclick="closePanel('buildingPanel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="building-grid" id="buildingGrid">
                    <!-- 建筑选项将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 军队界面 -->
        <div id="armyPanel" class="panel hidden">
            <div class="panel-header">
                <h3>军队管理</h3>
                <button class="close-btn" onclick="closePanel('armyPanel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="army-tabs">
                    <button class="tab-btn active" data-tab="recruit">招募</button>
                    <button class="tab-btn" data-tab="manage">管理</button>
                </div>
                <div class="tab-content" id="armyTabContent">
                    <!-- 军队内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 科技界面 -->
        <div id="techPanel" class="panel hidden">
            <div class="panel-header">
                <h3>科技研发</h3>
                <button class="close-btn" onclick="closePanel('techPanel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="tech-tree" id="techTree">
                    <!-- 科技树将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 战斗界面 -->
        <div id="battlePanel" class="panel hidden">
            <div class="panel-header">
                <h3>战斗</h3>
                <button class="close-btn" onclick="closePanel('battlePanel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="battle-options">
                    <button class="battle-btn" id="pveBtn">PVE战斗</button>
                    <button class="battle-btn" id="pvpBtn">PVP对战</button>
                </div>
                <div class="battle-list" id="battleList">
                    <!-- 战斗列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 任务界面 -->
        <div id="questPanel" class="panel hidden">
            <div class="panel-header">
                <h3>任务中心</h3>
                <button class="close-btn" onclick="closePanel('questPanel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="quest-tabs">
                    <button class="tab-btn active" data-tab="daily">日常</button>
                    <button class="tab-btn" data-tab="main">主线</button>
                    <button class="tab-btn" data-tab="achievement">成就</button>
                </div>
                <div class="tab-content" id="questTabContent">
                    <!-- 任务内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 通用确认对话框 -->
        <div id="confirmDialog" class="dialog hidden">
            <div class="dialog-content">
                <h4 id="confirmTitle">确认</h4>
                <p id="confirmMessage">确定要执行此操作吗？</p>
                <div class="dialog-buttons">
                    <button id="confirmYes" class="btn btn-primary">确定</button>
                    <button id="confirmNo" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <div id="messageContainer" class="message-container"></div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils/Utils.js"></script>
    <script src="js/utils/EventSystem.js"></script>
    <script src="js/utils/SaveSystem.js"></script>
    <script src="js/game/GameEngine.js"></script>
    <script src="js/game/ResourceManager.js"></script>
    <script src="js/game/BuildingSystem.js"></script>
    <script src="js/game/ArmySystem.js"></script>
    <script src="js/game/TechSystem.js"></script>
    <script src="js/game/BattleSystem.js"></script>
    <script src="js/ui/UIManager.js"></script>
    <script src="js/ui/MainUI.js"></script>
    <script src="js/ui/BuildingUI.js"></script>
    <script src="js/ui/BattleUI.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
